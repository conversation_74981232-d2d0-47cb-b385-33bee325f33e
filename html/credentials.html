<!DOCTYPE html>

<html lang="en" data-content_root="./">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>Credentials &#8212; pipenv 2024.3.0 documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="_static/basic.css?v=686e5160" />
    <link rel="stylesheet" type="text/css" href="_static/alabaster.css?v=27fed22d" />
    <link rel="stylesheet" type="text/css" href="_static/custom.css?v=24e4e28d" />
    <script src="_static/documentation_options.js?v=7b2d7c30"></script>
    <script src="_static/doctools.js?v=9bcbadda"></script>
    <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Environment and Shell Configuration" href="shell.html" />
    <link rel="prev" title="Specifying Package Indexes" href="indexes.html" />
   
  <link rel="stylesheet" href="_static/custom.css" type="text/css" />
  

  
  

  </head><body>
  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          

          <div class="body" role="main">
            
  <section class="tex2jax_ignore mathjax_ignore" id="credentials">
<h1>Credentials<a class="headerlink" href="#credentials" title="Link to this heading">¶</a></h1>
<section id="injecting-credentials-into-pipfile-via-environment-variables">
<h2>Injecting credentials into Pipfile via environment variables<a class="headerlink" href="#injecting-credentials-into-pipfile-via-environment-variables" title="Link to this heading">¶</a></h2>
<p>Pipenv will expand environment variables (if defined) in your Pipfile. Quite
useful if you need to authenticate to a private PyPI:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>[[source]]
url = &quot;https://$USERNAME:${PASSWORD}@mypypi.example.com/simple&quot;
verify_ssl = true
name = &quot;pypi&quot;
</pre></div>
</div>
<p>Luckily - pipenv will hash your Pipfile <em>before</em> expanding environment
variables (and, helpfully, will substitute the environment variables again when
you install from the lock file - so no need to commit any secrets! Woo!)</p>
<p>If your credentials contain special characters, make sure they are URL-encoded as specified in <code class="docutils literal notranslate"><span class="pre">rfc3986</span> <span class="pre">&lt;https://datatracker.ietf.org/doc/html/rfc3986&gt;</span></code>_.</p>
<p>Environment variables may be specified as <code class="docutils literal notranslate"><span class="pre">${MY_ENVAR}</span></code> or <code class="docutils literal notranslate"><span class="pre">$MY_ENVAR</span></code>.</p>
<p>On Windows, <code class="docutils literal notranslate"><span class="pre">%MY_ENVAR%</span></code> is supported in addition to <code class="docutils literal notranslate"><span class="pre">${MY_ENVAR}</span></code> or <code class="docutils literal notranslate"><span class="pre">$MY_ENVAR</span></code>.</p>
<p>Environment variables in the URL part of requirement specifiers can also be expanded, where the variable must be in the form of <code class="docutils literal notranslate"><span class="pre">${VAR_NAME}</span></code>. Neither <code class="docutils literal notranslate"><span class="pre">$VAR_NAME</span></code> nor <code class="docutils literal notranslate"><span class="pre">%VAR_NAME%</span></code> is acceptable:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>[[package]]
requests = {git = &quot;git://${USERNAME}:${PASSWORD}@private.git.com/psf/requests.git&quot;, ref = &quot;2.22.0&quot;}
</pre></div>
</div>
<p>Keep in mind that environment variables are expanded in runtime, leaving the entries in <code class="docutils literal notranslate"><span class="pre">Pipfile</span></code> or <code class="docutils literal notranslate"><span class="pre">Pipfile.lock</span></code> untouched. This is to avoid the accidental leakage of credentials in the source code.</p>
</section>
<section id="injecting-credentials-through-keychain-support">
<h2>Injecting credentials through keychain support<a class="headerlink" href="#injecting-credentials-through-keychain-support" title="Link to this heading">¶</a></h2>
<p>Private registries on Google Cloud, Azure and AWS support dynamic credentials using
the keychain implementation.</p>
<p>Pipenv supports this keychain implementation. It will automatically detect the
keychain implementation and use it to authenticate to the private registry.</p>
<section id="google-cloud">
<h3>Google Cloud<a class="headerlink" href="#google-cloud" title="Link to this heading">¶</a></h3>
<p>Google Cloud supports private registries. You can find more information about
this here: <a class="reference external" href="https://cloud.google.com/artifact-registry/docs/python/authentication">https://cloud.google.com/artifact-registry/docs/python/authentication</a></p>
<p>In order to utilize, you need to install the <code class="docutils literal notranslate"><span class="pre">keyring</span></code> and <code class="docutils literal notranslate"><span class="pre">keyrings.google-artifactregistry</span></code> packages,
and they must be available in the same virtualenv that you intend to use Pipenv in.</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>pipenv run pip install keyring keyrings.google-artifactregistry-auth
</pre></div>
</div>
<p>Depending on the way your keychain is structured, it may ask for user input.
Asking the user for input is disabled by default, and this may disable the keychain support completely.
If you want to work with private registries that use the keychain for authentication,
you may need to disable the “enforcement of no input”.</p>
<p><strong>Note:</strong> Please be sure that the keychain will really not ask for input.
Otherwise, the process will hang forever!:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>[[source]]
url = &quot;https://pypi.org/simple&quot;
verify_ssl = true
name = &quot;pypi&quot;

[[source]]
url = &quot;https://europe-python.pkg.dev/my-project/python/simple&quot;
verify_ssl = true
name = &quot;private-gcp&quot;

[packages]
flask = &quot;*&quot;
private-test-package = {version = &quot;*&quot;, index = &quot;private-gcp&quot;}

[pipenv]
disable_pip_input = false
</pre></div>
</div>
<p>Above example will install <code class="docutils literal notranslate"><span class="pre">flask</span></code> and a private package <code class="docutils literal notranslate"><span class="pre">private-test-package</span></code> from GCP.</p>
</section>
</section>
</section>


          </div>
          
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper"><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/docsearch.js@2/dist/cdn/docsearch.min.css" />
<style>
  .algolia-autocomplete{
    width: 100%;
    height: 1.5em
  }
  .algolia-autocomplete a{
    border-bottom: none !important;
  }
  iframe.noScrolling{
    overflow: hidden;
  }
  iframe.noBorder{
    border: none;
  }
  #doc_search{
    width: 100%;
    height: 100%;
  }
  </style>
  <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/docsearch.js@2/dist/cdn/docsearch.min.js" onload="docsearch({
    apiKey: '0dbb76467f0c180a1344fc46858df17b',
    indexName: 'pipenv',
    inputSelector: '#doc_search',
    debug: false // Set debug to true if you want to inspect the dropdown
  })" async></script>
<p class="logo">
  <a href="index.html">
    <img class="logo" src="_static/pipenv.png" title="Gift-wrapped box w/ 'pipenv' on the ribbon"/>
  </a>
</p>

<p>
  <iframe src="https://ghbtns.com/github-btn.html?user=pypa&repo=pipenv&type=watch&count=true&size=large"
    allowtransparency="true" class="noScrolling noBorder" width="200px" height="35px" ></iframe>
</p>
<input id="doc_search" label="doc_search" placeholder="Search the doc" autofocus/>
<p>
  <strong>Pipenv</strong> is a production-ready tool that aims to bring the best of all packaging worlds to the Python world. It harnesses Pipfile, pip, and virtualenv into one single command.
  <p>It features very pretty terminal colors.</p>
</p>

<h3>Stay Informed</h3>
<p>Receive updates on new releases and upcoming projects.</p>

<p><iframe src="https://ghbtns.com/github-btn.html?user=pypa&type=follow&count=true"
  allowtransparency="true" class="noScrolling noBorder" width="200" height="20"></iframe></p>

<p><a href="https://twitter.com/ThePyPA" class="twitter-follow-button" data-show-count="false">Follow @ThePyPA</a> <script>!function(d,s,id){var js,fjs=d.getElementsByTagName(s)[0],p=/^http:/.test(d.location)?'http':'https';if(!d.getElementById(id)){js=d.createElement(s);js.id=id;js.src=p+'://platform.twitter.com/widgets.js';fjs.parentNode.insertBefore(js,fjs);}}(document, 'script', 'twitter-wjs');</script></p>
<p><a href="https://mail.python.org/mailman3/lists/distutils-sig.python.org/">Join Mailing List</a>.</p>

<h3>Other Projects</h3>

<ul>
    <li><a href="https://pipenv-pipes.readthedocs.io/en/latest/">Pipenv-Pipes</a></li>
    <li><a href="https://pip.pypa.io/en/stable/">pip: package installer for Python</a></li>
    <li><a href="https://requests.readthedocs.io">Requests: HTTP for Humans</a></li>
</ul>


<h3>Useful Links</h3>
<ul>
    <li><a href="https://github.com/pypa/pipenv">Pipenv @ GitHub</a></li>
    <li><a href="https://pypi.org/project/pipenv">Pipenv @ PyPI</a></li>
    <li><a href="https://launchpad.net/~pypa/+archive/ubuntu/ppa">Pipenv PPA (PyPA)</a></li>
    <li><a href="https://github.com/pypa/pipenv/issues">Issue Tracker</a></li>
    <hr>
    <li><a href="https://pipenv-ja.readthedocs.io/ja/translate-ja/">日本語</a></li>
    <li><a href="https://pipenv-es.readthedocs.io/es/latest/">Español</a></li>
</ul>
  <div>
    <h3><a href="index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Credentials</a><ul>
<li><a class="reference internal" href="#injecting-credentials-into-pipfile-via-environment-variables">Injecting credentials into Pipfile via environment variables</a></li>
<li><a class="reference internal" href="#injecting-credentials-through-keychain-support">Injecting credentials through keychain support</a><ul>
<li><a class="reference internal" href="#google-cloud">Google Cloud</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div><div class="relations">
<h3>Related Topics</h3>
<ul>
  <li><a href="index.html">Documentation overview</a><ul>
      <li>Previous: <a href="indexes.html" title="previous chapter">Specifying Package Indexes</a></li>
      <li>Next: <a href="shell.html" title="next chapter">Environment and Shell Configuration</a></li>
  </ul></li>
</ul>
</div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/credentials.md.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<search id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</search>
<script>document.getElementById('searchbox').style.display = "block"</script><script type="text/javascript">$('#searchbox').hide(0);</script>
<!--Alabaster (krTheme++) Hacks -->

<!-- CSS Adjustments (I'm very picky.) -->
<style type="text/css">

  /* Rezzy requires precise alignment. */
  img.logo {margin-left: -20px!important;}

  /* "Quick Search" should be not be shown for now. */
  div#searchbox h3 {display: none;}

  /* Make the document a little wider, less code is cut-off. */
  div.document {width: 1008px;}

  /* Much-improved spacing around code blocks. */
  div.highlight pre {padding: 11px 14px;}

  /* Remain Responsive! */
  @media screen and (max-width: 1008px) {
    div.document {width: 100%!important;}

    /* Have code blocks escape the document right-margin. */
    div.highlight pre {margin-right: -30px;}
  }

</style>

<!-- There are no more hacks. -->
<!--         இڿڰۣ-ڰۣ—         -->
<!--   Love, Kenneth Reitz    -->

<script src="_static//konami.js"></script>
<script>
  var easter_egg = new Konami('https://www.myfortunecookie.co.uk/fortunes/' + (Math.floor(Math.random() * 152) + 1));
</script>

<style>
  .injected {
    display: none!important;
  }

</style>

<!-- GitHub Logo -->
<a href="https://github.com/pypa/pipenv" class="github-corner" aria-label="View source on GitHub">
  <svg width="80" height="80" viewBox="0 0 250 250" style="fill:#151513; color:#fff; position: absolute; top: 0; border: 0; right: 0;" aria-hidden="true">
    <path d="M0,0 L115,115 L130,115 L142,142 L250,250 L250,0 Z"></path>
    <path d="M128.3,109.0 C113.8,99.7 119.0,89.6 119.0,89.6 C122.0,82.7 120.5,78.6 120.5,78.6 C119.2,72.0 123.4,76.3 123.4,76.3 C127.3,80.9 125.5,87.3 125.5,87.3 C122.9,97.6 130.6,101.9 134.4,103.2" fill="currentColor" style="transform-origin: 130px 106px;" class="octo-arm"></path>
    <path d="M115.0,115.0 C114.9,115.1 118.7,116.5 119.8,115.4 L133.7,101.6 C136.9,99.2 139.9,98.4 142.2,98.6 C133.8,88.0 127.5,74.4 143.8,58.0 C148.5,53.4 154.0,51.2 159.7,51.0 C160.3,49.4 163.2,43.6 171.4,40.1 C171.4,40.1 176.1,42.5 178.8,56.2 C183.1,58.6 187.2,61.8 190.9,65.4 C194.5,69.0 197.7,73.2 200.1,77.6 C213.8,80.2 216.3,84.9 216.3,84.9 C212.7,93.1 206.9,96.0 205.4,96.6 C205.1,102.4 203.0,107.8 198.3,112.5 C181.9,128.9 168.3,122.5 157.7,114.1 C157.9,116.9 156.7,120.9 152.7,124.9 L141.0,136.5 C139.8,137.7 141.6,141.9 141.8,141.8 Z" fill="currentColor" class="octo-body"></path>
  </svg>
</a>
<style>.github-corner:hover .octo-arm{animation:octocat-wave 560ms ease-in-out}@keyframes octocat-wave{0%,100%{transform:rotate(0)}20%,60%{transform:rotate(-25deg)}40%,80%{transform:rotate(10deg)}}@media (max-width:500px){.github-corner:hover .octo-arm{animation:none}.github-corner .octo-arm{animation:octocat-wave 560ms ease-in-out}}</style>


<!-- That was not a hack. That was art. -->

<!-- UserVoice JavaScript SDK (only needed once on a page) -->
<script>(function(){var uv=document.createElement('script');uv.type='text/javascript';uv.async=true;uv.src='//widget.uservoice.com/f4AQraEfwInlMzkexfRLg.js';var s=document.getElementsByTagName('script')[0];s.parentNode.insertBefore(uv,s)})()</script>

<!-- A tab to launch the Classic Widget -->
<script>
UserVoice = window.UserVoice || [];
UserVoice.push(['showTab', 'classic_widget', {
  mode: 'feedback',
  primary_color: '#fa8c28',
  link_color: '#0a8cc6',
  forum_id: 913660,
  tab_label: 'Got feedback?',
  tab_color: '#00994f',
  tab_position: 'bottom-left',
  tab_inverted: true
}]);
</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="footer">
      &#169;2020. A project founded by Kenneth Reitz and maintained by <a href="https://www.pypa.io/en/latest/">Python Packaging Authority (PyPA).</a>.
      
      |
      <a href="_sources/credentials.md.txt"
          rel="nofollow">Page source</a>
    </div>

    

    
  </body>
</html>