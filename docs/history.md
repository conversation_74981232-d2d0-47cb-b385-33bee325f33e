# History

## From Origins To DjangoCon 2008

The idea for Pinax was seeded in 2007 when <PERSON> was developing a number of websites (including [Quisition](https://quisition.com/) and [Habitualist](https://habitualist.com/)) using Django and realized how much of the code he was writing or intending to write was similar across websites.

<PERSON> became interested in developing a set of reusable Django apps and developing conventions around what such reusable apps should look like. A group called the "Hotclub of France" (or `django-hotclub`) was formed after PyCon 2007, the name coming from the name of <PERSON><PERSON><PERSON>'s band. Not much work (or even discussion) took place the rest of the 2007. There was some discussion and a little bit of sprinting on individual reusable apps at PyCon 2008.

In early May 2008, <PERSON> suggested to the `django-hotclub` mailing list the need for a project that could be "an out-of-the-box Django-based website with everything but the domain-specific functionality". <PERSON> also suggested it could be a useful scaffolding for writing and trying out reusable apps. He started what he initially called "Tabula Rasa", shortly after renamed to "Pinax", a suggestion by <PERSON><PERSON><PERSON>.

On Memorial Day weekend 2008, <PERSON> decided to spend the weekend hacking on this new Pinax project and was joined by other "hotclub" members <PERSON>, <PERSON><PERSON>, and <PERSON>. By the end of the weekend, with help from others, Pinax had user profile pages, gravatars, user-to-user messages, announcements, OpenID support, join invitations, a basic Twitter clone with OEmbed support, groups and localizations into German, Spanish, and Swedish.

By July, Pinax added wikis, threaded discussions, bookmarks with voting, contact import, blogs with tagging and localization into Brazilian Portuguese and Hebrew.

TODO: THE BREAK OUT OF CLOUD27

TODO: DJANGOCON 2008

TODO: THE 0.7.X ERA

TODO: THE 0.9.X ERA



## Some Historical Blog Posts

* [Reusable Django Apps And Introducing Tabula Rasa](http://jtauber.com/blog/2008/05/06/reusable_django_apps_and_introducing_tabula_rasa/) *May 6, 2008*
* [Introducing Pinax](http://jtauber.com/blog/2008/05/10/introducing_pinax/) *May 10, 2008*
* [Pinax Progress](http://jtauber.com/blog/2008/05/25/pinax_progress/) *May 25, 2008*
* [Pinax Progress II](http://jtauber.com/blog/2008/05/26/pinax_progress_ii/) *May 26, 2008*
* [Pinax Progress III](http://jtauber.com/blog/2008/07/02/pinax_progress_iii/) *July 2, 2008*
* [Pinax Project and Cloud27](http://jtauber.com/blog/2008/07/30/pinax_project_and_cloud27/) *July 30, 2008*
* [DjangoCon, Pinax and Cloud27](http://jtauber.com/blog/2008/09/08/djangocon_pinax_and_cloud27/) *September 8, 2008*
