# Pinax in the Wild

Pinax is used to build all sorts of things. This is just a small sampling of
some of the things that can be built with Pinax. If you have built something
please either [create an issue](https://github.com/pinax/pinax/issues/new)
and tell us about it or [fork this repo](https://github.com/pinax/pinax/issues#fork-destination-box),
update this document, and send a pull request.


## Pinax Blog

http://blog.pinaxproject.com/

* [Blog starter project](https://github.com/pinax/pinax-starter-projects#pinax-project-blog)
    * [pinax-blog](https://github.com/pinax/pinax-blog) for the blog application
* [pinax-pages](https://github.com/pinax/pinax-pages) to add some basic CMS functionality


## Django CMS Light

http://django-cms-light.com

KISS multi-site CMS project optimized for hackers based on git and markdown like django and pelican had a baby with the purpose of hacking on local economy growth for the greater good.

* Based on social auth starter project (https://github.com/pinax/pinax-starter-projects/tree/social-auth)
* Uses [Pinax Theme Bootstrap](https://github.com/pinax/pinax-theme-bootstrap)
* Uses [Pinax Web Analytics](https://github.com/pinax/pinax-webanalytics)
* Uses [Pinax Eventlog](https://github.com/pinax/pinax-eventlog)
