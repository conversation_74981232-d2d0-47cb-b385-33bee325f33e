{"info": {"author": "<PERSON><PERSON><PERSON><PERSON>", "author_email": "<EMAIL>", "bugtrack_url": null, "classifiers": ["License :: OSI Approved :: MIT License", "Programming Language :: Python :: 2", "Programming Language :: Python :: 2.7", "Programming Language :: Python :: 3", "Programming Language :: Python :: 3.4", "Programming Language :: Python :: 3.5", "Programming Language :: Python :: 3.6", "Programming Language :: Python :: 3.7"], "description": "[github_release]: https://img.shields.io/github/release/sdispater/tomlkit.svg?logo=github&logoColor=white\n[pypi_version]: https://img.shields.io/pypi/v/tomlkit.svg?logo=python&logoColor=white\n[python_versions]: https://img.shields.io/pypi/pyversions/tomlkit.svg?logo=python&logoColor=white\n[github_license]: https://img.shields.io/github/license/sdispater/tomlkit.svg?logo=github&logoColor=white\n[travisci]: https://img.shields.io/travis/com/sdispater/tomlkit/master.svg?logo=travis&logoColor=white&label=Travis%20CI\n[appveyor]: https://img.shields.io/appveyor/ci/sdispater/tomlkit/master.svg?logo=appveyor&logoColor=white&label=AppVeyor\n<!--Codecov logo not offered by shields.io or simpleicons.org, this is Codecov's SVG image modified to be white-->\n[codecov]: https://img.shields.io/codecov/c/github/sdispater/tomlkit/master.svg?logo=data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNDgiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CgogPGc+CiAgPHRpdGxlPmJhY2tncm91bmQ8L3RpdGxlPgogIDxyZWN0IGZpbGw9Im5vbmUiIGlkPSJjYW52YXNfYmFja2dyb3VuZCIgaGVpZ2h0PSI0MDIiIHdpZHRoPSI1ODIiIHk9Ii0xIiB4PSItMSIvPgogPC9nPgogPGc+CiAgPHRpdGxlPkxheWVyIDE8L3RpdGxlPgogIDxwYXRoIGlkPSJzdmdfMSIgZmlsbC1ydWxlPSJldmVub2RkIiBmaWxsPSIjZmZmZmZmIiBkPSJtMjUuMDE0LDBjLTEzLjc4NCwwLjAxIC0yNS4wMDQsMTEuMTQ5IC0yNS4wMTQsMjQuODMybDAsMC4wNjJsNC4yNTQsMi40ODJsMC4wNTgsLTAuMDM5YTEyLjIzOCwxMi4yMzggMCAwIDEgOS4wNzgsLTEuOTI4YTExLjg0NCwxMS44NDQgMCAwIDEgNS45OCwyLjk3NWwwLjczLDAuNjhsMC40MTMsLTAuOTA0YzAuNCwtMC44NzQgMC44NjIsLTEuNjk2IDEuMzc0LC0yLjQ0M2MwLjIwNiwtMC4zIDAuNDMzLC0wLjYwNCAwLjY5MiwtMC45MjlsMC40MjcsLTAuNTM1bC0wLjUyNiwtMC40NGExNy40NSwxNy40NSAwIDAgMCAtOC4xLC0zLjc4MWExNy44NTMsMTcuODUzIDAgMCAwIC04LjM3NSwwLjQ5YzIuMDIzLC04Ljg2OCA5LjgyLC0xNS4wNSAxOS4wMjcsLTE1LjA1N2M1LjE5NSwwIDEwLjA3OCwyLjAwNyAxMy43NTIsNS42NTJjMi42MTksMi41OTggNC40MjIsNS44MzUgNS4yMjQsOS4zNzJhMTcuOTA4LDE3LjkwOCAwIDAgMCAtNS4yMDgsLTAuNzlsLTAuMzE4LC0wLjAwMWExOC4wOTYsMTguMDk2IDAgMCAwIC0yLjA2NywwLjE1M2wtMC4wODcsMC4wMTJjLTAuMzAzLDAuMDQgLTAuNTcsMC4wODEgLTAuODEzLDAuMTI2Yy0wLjExOSwwLjAyIC0wLjIzNywwLjA0NSAtMC4zNTUsMC4wNjhjLTAuMjgsMC4wNTcgLTAuNTU0LDAuMTE5IC0wLjgxNiwwLjE4NWwtMC4yODgsMC4wNzNjLTAuMzM2LDAuMDkgLTAuNjc1LDAuMTkxIC0xLjAwNiwwLjNsLTAuMDYxLDAuMDJjLTAuNzQsMC4yNTEgLTEuNDc4LDAuNTU4IC0yLjE5LDAuOTE0bC0wLjA1NywwLjAyOWMtMC4zMTYsMC4xNTggLTAuNjM2LDAuMzMzIC0wLjk3OCwwLjUzNGwtMC4wNzUsMC4wNDVhMTYuOTcsMTYuOTcgMCAwIDAgLTQuNDE0LDMuNzhsLTAuMTU3LDAuMTkxYy0wLjMxNywwLjM5NCAtMC41NjcsMC43MjcgLTAuNzg3LDEuMDQ4Yy0wLjE4NCwwLjI3IC0wLjM2OSwwLjU2IC0wLjYsMC45NDJsLTAuMTI2LDAuMjE3Yy0wLjE4NCwwLjMxOCAtMC4zNDgsMC42MjIgLTAuNDg3LDAuOWwtMC4wMzMsMC4wNjFjLTAuMzU0LDAuNzExIC0wLjY2MSwxLjQ1NSAtMC45MTcsMi4yMTRsLTAuMDM2LDAuMTExYTE3LjEzLDE3LjEzIDAgMCAwIC0wLjg1NSw1LjY0NGwwLjAwMywwLjIzNGEyMy41NjUsMjMuNTY1IDAgMCAwIDAuMDQzLDAuODIyYzAuMDEsMC4xMyAwLjAyMywwLjI1OSAwLjAzNiwwLjM4OGMwLjAxNSwwLjE1OCAwLjAzNCwwLjMxNiAwLjA1MywwLjQ3MWwwLjAxMSwwLjA4OGwwLjAyOCwwLjIxNGMwLjAzNywwLjI2NCAwLjA4LDAuNTI1IDAuMTMsMC43ODdjMC41MDMsMi42MzcgMS43Niw1LjI3NCAzLjYzNSw3LjYyNWwwLjA4NSwwLjEwNmwwLjA4NywtMC4xMDRjMC43NDgsLTAuODg0IDIuNjAzLC0zLjY4NyAyLjc2LC01LjM2OWwwLjAwMywtMC4wMzFsLTAuMDE1LC0wLjAyOGExMS43MzYsMTEuNzM2IDAgMCAxIC0xLjMzMywtNS40MDdjMCwtNi4yODQgNC45NCwtMTEuNTAyIDExLjI0MywtMTEuODhsMC40MTQsLTAuMDE1YzIuNTYxLC0wLjA1OCA1LjA2NCwwLjY3MyA3LjIzLDIuMTM2bDAuMDU4LDAuMDM5bDQuMTk3LC0yLjQ0bDAuMDU1LC0wLjAzM2wwLC0wLjA2MmMwLjAwNiwtNi42MzIgLTIuNTkyLC0xMi44NjUgLTcuMzE0LC0xNy41NTFjLTQuNzE2LC00LjY3OSAtMTAuOTkxLC03LjI1NSAtMTcuNjcyLC03LjI1NSIvPgogPC9nPgo8L3N2Zz4=&label=Codecov\n\n[![GitHub Release][github_release]](https://github.com/sdispater/tomlkit/releases/)\n[![PyPI Version][pypi_version]](https://pypi.python.org/pypi/tomlkit/)\n[![Python Versions][python_versions]](https://pypi.python.org/pypi/tomlkit/)\n[![License][github_license]](https://github.com/sdispater/tomlkit/blob/master/LICENSE)\n<br>\n[![Travis CI][travisci]](https://travis-ci.com/sdispater/tomlkit)\n[![AppVeyor][appveyor]](https://ci.appveyor.com/project/sdispater/tomlkit)\n[![Codecov][codecov]](https://codecov.io/gh/sdispater/tomlkit)\n\n# TOML Kit - Style-preserving TOML library for Python\n\nTOML Kit is a **0.5.0-compliant** [TOML](https://github.com/toml-lang/toml) library.\n\nIt includes a parser that preserves all comments, indentations, whitespace and internal element ordering,\nand makes them accessible and editable via an intuitive API.\n\nYou can also create new TOML documents from scratch using the provided helpers.\n\nPart of the implementation as been adapted, improved and fixed from [Molten](https://github.com/LeopoldArkham/Molten).\n\n## Usage\n\n### Parsing\n\nTOML Kit comes with a fast and style-preserving parser to help you access\nthe content of TOML files and strings.\n\n```python\n>>> from tomlkit import dumps\n>>> from tomlkit import parse  # you can also use loads\n\n>>> content = \"\"\"[table]\n... foo = \"bar\"  # String\n... \"\"\"\n>>> doc = parse(content)\n\n# doc is a TOMLDocument instance that holds all the information\n# about the TOML string.\n# It behaves like a standard dictionary.\n\n>>> assert doc[\"table\"][\"foo\"] == \"bar\"\n\n# The string generated from the document is exactly the same\n# as the original string\n>>> assert dumps(doc) == content\n```\n\n### Modifying\n\nTOML Kit provides an intuitive API to modify TOML documents.\n\n```python\n>>> from tomlkit import dumps\n>>> from tomlkit import parse\n>>> from tomlkit import table\n\n>>> doc = parse(\"\"\"[table]\n... foo = \"bar\"  # String\n... \"\"\")\n\n>>> doc[\"table\"][\"baz\"] = 13\n\n>>> dumps(doc)\n\"\"\"[table]\nfoo = \"bar\"  # String\nbaz = 13\n\"\"\"\n\n# Add a new table\n>>> tab = table()\n>>> tab.add(\"array\", [1, 2, 3])\n\n>>> doc[\"table2\"] = tab\n\n>>> dumps(doc)\n\"\"\"[table]\nfoo = \"bar\"  # String\nbaz = 13\n\n[table2]\narray = [1, 2, 3]\n\"\"\"\n\n# Remove the newly added table\n>>> doc.remove(\"table2\")\n# del doc[\"table2] is also possible\n```\n\n### Writing\n\nYou can also write a new TOML document from scratch.\n\nLet's say we want to create this following document:\n\n```toml\n# This is a TOML document.\n\ntitle = \"TOML Example\"\n\n[owner]\nname = \"Tom Preston-Werner\"\norganization = \"GitHub\"\nbio = \"GitHub Cofounder & CEO\\nLikes tater tots and beer.\"\ndob = 1979-05-27T07:32:00Z # First class dates? Why not?\n\n[database]\nserver = \"***********\"\nports = [ 8001, 8001, 8002 ]\nconnection_max = 5000\nenabled = true\n```\n\nIt can be created with the following code:\n\n```python\n>>> from tomlkit import comment\n>>> from tomlkit import document\n>>> from tomlkit import nl\n>>> from tomlkit import table\n\n>>> doc = document()\n>>> doc.add(comment(\"This is a TOML document.\"))\n>>> doc.add(nl())\n>>> doc.add(\"title\", \"TOML Example\")\n# Using doc[\"title\"] = \"TOML Example\" is also possible\n\n>>> owner = table()\n>>> owner.add(\"name\", \"Tom Preston-Werner\")\n>>> owner.add(\"organization\", \"GitHub\")\n>>> owner.add(\"bio\", \"GitHub Cofounder & CEO\\nLikes tater tots and beer.\")\n>>> owner.add(\"dob\", datetime(1979, 5, 27, 7, 32, tzinfo=utc))\n>>> owner[\"dob\"].comment(\"First class dates? Why not?\")\n\n# Adding the table to the document\n>>> doc.add(\"owner\", owner)\n\n>>> database = table()\n>>> database[\"server\"] = \"***********\"\n>>> database[\"ports\"] = [8001, 8001, 8002]\n>>> database[\"connection_max\"] = 5000\n>>> database[\"enabled\"] = True\n\n>>> doc[\"database\"] = database\n```\n\n\n## Installation\n\nIf you are using [Poetry](https://poetry.eustace.io),\nadd `tomlkit` to your `pyproject.toml` file by using:\n\n```bash\npoetry add tomlkit\n```\n\nIf not, you can use `pip`:\n\n```bash\npip install tomlkit\n```\n", "description_content_type": "text/markdown", "docs_url": null, "download_url": "", "downloads": {"last_day": -1, "last_month": -1, "last_week": -1}, "home_page": "https://github.com/sdispater/tomlkit", "keywords": "", "license": "MIT", "maintainer": "<PERSON><PERSON><PERSON><PERSON>", "maintainer_email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>", "package_url": "https://pypi.org/project/tomlkit/", "platform": "", "project_url": "https://pypi.org/project/tomlkit/", "project_urls": {"Homepage": "https://github.com/sdispater/tomlkit", "Repository": "https://github.com/sdispater/tomlkit"}, "release_url": "https://pypi.org/project/tomlkit/0.5.3/", "requires_dist": ["enum34 (>=1.1,<2.0); python_version >= \"2.7\" and python_version < \"2.8\"", "functools32 (>=3.2.3,<4.0.0); python_version >= \"2.7\" and python_version < \"2.8\"", "typing (>=3.6,<4.0); python_version >= \"2.7\" and python_version < \"2.8\" or python_version >= \"3.4\" and python_version < \"3.5\""], "requires_python": ">=2.7, !=3.0.*, !=3.1.*, !=3.2.*, !=3.3.*", "summary": "Style preserving TOML library", "version": "0.5.3"}, "last_serial": 4504211, "releases": {"0.5.3": [{"comment_text": "", "digests": {"md5": "0a6cf417df5d0fc911f89447c9a662a9", "sha256": "f077456d35303e7908cc233b340f71e0bec96f63429997f38ca9272b7d64029e"}, "downloads": -1, "filename": "to<PERSON><PERSON><PERSON>-0.5.3-py2.py3-none-any.whl", "has_sig": false, "md5_digest": "0a6cf417df5d0fc911f89447c9a662a9", "packagetype": "bdist_wheel", "python_version": "py2.py3", "requires_python": ">=2.7, !=3.0.*, !=3.1.*, !=3.2.*, !=3.3.*", "size": 116796, "upload_time": "2018-11-19T20:05:37", "url": "https://files.pythonhosted.org/packages/71/c6/06c014b92cc48270765d6a9418d82239b158d8a9b69e031b0e2c6598740b/tomlkit-0.5.3-py2.py3-none-any.whl"}, {"comment_text": "", "digests": {"md5": "a708470b53d689013f2fc9f0a7902adf", "sha256": "d6506342615d051bc961f70bfcfa3d29b6616cc08a3ddfd4bc24196f16fd4ec2"}, "downloads": -1, "filename": "tomlkit-0.5.3.tar.gz", "has_sig": false, "md5_digest": "a708470b53d689013f2fc9f0a7902adf", "packagetype": "sdist", "python_version": "source", "requires_python": ">=2.7, !=3.0.*, !=3.1.*, !=3.2.*, !=3.3.*", "size": 29864, "upload_time": "2018-11-19T20:05:39", "url": "https://files.pythonhosted.org/packages/f7/f7/bbd9213bfe76cb7821c897f9ed74877fd74993b4ca2fe9513eb5a31030f9/tomlkit-0.5.3.tar.gz"}]}, "urls": [{"comment_text": "", "digests": {"md5": "0a6cf417df5d0fc911f89447c9a662a9", "sha256": "f077456d35303e7908cc233b340f71e0bec96f63429997f38ca9272b7d64029e"}, "downloads": -1, "filename": "to<PERSON><PERSON><PERSON>-0.5.3-py2.py3-none-any.whl", "has_sig": false, "md5_digest": "0a6cf417df5d0fc911f89447c9a662a9", "packagetype": "bdist_wheel", "python_version": "py2.py3", "requires_python": ">=2.7, !=3.0.*, !=3.1.*, !=3.2.*, !=3.3.*", "size": 116796, "upload_time": "2018-11-19T20:05:37", "url": "https://files.pythonhosted.org/packages/71/c6/06c014b92cc48270765d6a9418d82239b158d8a9b69e031b0e2c6598740b/tomlkit-0.5.3-py2.py3-none-any.whl"}, {"comment_text": "", "digests": {"md5": "a708470b53d689013f2fc9f0a7902adf", "sha256": "d6506342615d051bc961f70bfcfa3d29b6616cc08a3ddfd4bc24196f16fd4ec2"}, "downloads": -1, "filename": "tomlkit-0.5.3.tar.gz", "has_sig": false, "md5_digest": "a708470b53d689013f2fc9f0a7902adf", "packagetype": "sdist", "python_version": "source", "requires_python": ">=2.7, !=3.0.*, !=3.1.*, !=3.2.*, !=3.3.*", "size": 29864, "upload_time": "2018-11-19T20:05:39", "url": "https://files.pythonhosted.org/packages/f7/f7/bbd9213bfe76cb7821c897f9ed74877fd74993b4ca2fe9513eb5a31030f9/tomlkit-0.5.3.tar.gz"}]}