SHELL=/bin/bash -eu -o pipefail

# Download requirements from Sentry's requirements-base.txt
requirements.txt:
	curl -sL https://raw.githubusercontent.com/getsentry/sentry/51281a6abd8ff4a93d2cebc04e1d5fc7aa9c4c11/requirements-base.txt | grep -v -- --index-url > $@

# Random package to benchmark adding a new dependency
PACKAGE := goodconf

.PHONY: pipenv-clean-cache pipenv-clean-venv pipenv-clean-lock
pipenv-clean-cache:
	rm -rf ~/.cache/pip
	rm -rf ~/.cache/pipenv

pipenv-clean-venv:
	rm -rf $$(pipenv --venv 2>/dev/null || echo "./does-not-exist")

pipenv-clean-lock:
	rm -f Pipfile.lock

.PHONY: pipenv-tooling pipenv-import pipenv-lock pipenv-install pipenv-update pipenv-add-package pipenv-version
pipenv-tooling:
	# Install pipenv using the current development version
	pip install -e ..

pipenv-import:
	pipenv install -r requirements.txt

pipenv-lock:
	pipenv lock

pipenv-install:
	pipenv sync

pipenv-update:
	pipenv update

pipenv-add-package:
	pipenv install $(PACKAGE)

pipenv-version:
	@pipenv --version | awk '{print $$3}'

.PHONY: clean
clean:
	rm -f requirements.txt Pipfile Pipfile.lock
	$(MAKE) pipenv-clean-cache
	$(MAKE) pipenv-clean-venv
