[tox]
envlist =
    docs, packaging, py27, py35, py36, py37, coverage-report

[testenv]
passenv = CI GIT_SSL_CAINFO
setenv =
    LC_ALL = en_US.UTF-8
deps =
	coverage
	-e .[tests]
commands = coverage run --parallel -m pytest --timeout 300 []
install_command = python -m pip install {opts} {packages}
usedevelop = True

[testenv:coverage-report]
deps = coverage
skip_install = true
commands =
	coverage combine
	coverage report

[testenv:docs]
deps =
    -r{toxinidir}/docs/requirements.txt
    -e .[tests]
commands =
    sphinx-build -d {envtmpdir}/doctrees -b html docs docs/build/html
    sphinx-build -d {envtmpdir}/doctrees -b man  docs docs/build/man

[testenv:packaging]
deps =
    check-manifest
    readme_renderer
commands =
    check-manifest
    python setup.py check -m -r -s
