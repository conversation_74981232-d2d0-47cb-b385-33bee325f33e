import sys,types,os; p = os.path.join(sys._getframe(1).f_locals['sitedir'], *('nspkg2',)); ie = os.path.exists(os.path.join(p,'__init__.py')); m = not ie and sys.modules.setdefault('nspkg2',types.ModuleType('nspkg2')); mp = (m or []) and m.__dict__.setdefault('__path__',[]); (p not in mp) and mp.append(p)
import sys,types,os; p = os.path.join(sys._getframe(1).f_locals['sitedir'], *('nspkg2', 'bbb')); ie = os.path.exists(os.path.join(p,'__init__.py')); m = not ie and sys.modules.setdefault('nspkg2.bbb',types.ModuleType('nspkg2.bbb')); mp = (m or []) and m.__dict__.setdefault('__path__',[]); (p not in mp) and mp.append(p); m and setattr(sys.modules['nspkg2'], 'bbb', m)
