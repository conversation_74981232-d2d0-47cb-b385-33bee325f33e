# This is the pip requirements file for extensive
# PyInstaller testing.
#
# Example (assuming current dir is PyInstaller's top-level source dir)::
#
#   python -m pip install -r tests/requirements-libraries.txt  # extensive

# include requirements for base testing
-r requirements-tools.txt


# Needs work
# ----------
# These packages, if updated, produce test failures. Work needs to be done on
# these hooks.
#
# None at this time.


# Working
# -------
# These packages work with no (known) issues.
babel==2.6.0
boto==2.49.0
boto3==1.9.35
botocore==1.12.35
pygments==2.2.0
markdown==3.0.1
# simplejson is used for text_c_extension
simplejson==3.16.0
sphinx==1.8.1
pyzmq==17.1.2
# Required for test_namespace_package
zope.interface==4.6.0
numpy==1.15.3
scipy==1.1.0
lxml==4.2.5
keyring==16.0.0
openpyxl==2.5.9
pycparser==2.19
pytz==2018.7
sqlalchemy==1.2.13
zeep==3.1.0
pyexcelerate==0.7.3
Pillow==5.3.0
pinyin==0.4.0
pycryptodome==3.7.0
pycryptodomex==3.7.0
future==0.17.1
pyusb==1.0.2
Unidecode==1.0.22
h5py==2.8.0
python-dateutil==2.7.5
phonenumbers==8.9.16
requests==2.20.0


# Special cases
# -------------
# Url for downloading PyCrypto prebuilt Windows binaries:
# http://www.voidspace.org.uk/python/pycrypto-2.6.1/
# Required for crypto feature - encrypting bytecode.
pycrypto==2.6.1; python_version <= '3.5' or sys_platform != 'win32'

# Wheels are available only on OS X and Win 32. The only Windows 64-bit Python
# tests run are for Python 3.7, so exclude that. There's no way to simply
# exclude Windows 64 bit Python.
pyenchant==2.0.0; sys_platform == 'darwin' or (sys_platform == 'win32' and python_version <= '3.6')

# No wheel for Python 3.5 or higher.
PySide==1.2.4; sys_platform == 'win32' and python_version <= '3.4'

# As of 14-Aug-2018, twisted doesn't offer Python 3 wheels.
twisted==18.9.0; python_version == '2.7'

# Current gevent doesn't provide Windows Python 3.7 x32 wheels. Since we can't
# detect 32 vs. 64 bits, avoid Windows Python 3.7 altogether.
gevent==1.3.7; python_version != '3.7' or sys_platform != 'win32'
# Older gevent works on Windows Python 3.7 x32.
gevent==1.3.6; python_version == '3.7' and sys_platform == 'win32'  # pyup: ignore


# Python 3.5 and higher
# ---------------------
Django==2.1.3; python_version >= '3.5'
ipython==7.1.1; python_version>= '3.5'
matplotlib==3.0.1; python_version >= '3.5'
pandas==0.23.4; python_version >= '3.5'
pyqt5==5.11.3; python_version >= '3.5'
web3==4.8.1; python_version >= '3.5'

# uvloop does not currently support Windows.
uvloop==0.11.3; python_version >= '3.5' and sys_platform != 'win32'


# Python 3 only packages
# ----------------------
pylint==2.1.1; python_version != '2.7'
