[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[dev-packages]
pipenv = {path = ".", editable = true, extras = ["tests", "dev"]}
sphinx = "*"
sphinx-click = "==4.*"
sphinxcontrib-spelling = "==7.*"
click = "==8.0.3"
stdeb = {version="*", sys_platform = "== 'linux'"}
pre-commit = "==2.*"
atomicwrites = {version = "*", sys_platform = "== 'win32'"}
pytest-cov = "==4.*"
typing-extensions = "==4.*"
waitress = {version = "3.*", sys_platform = "== 'win32'"}
gunicorn = {version = "23.0.*", sys_platform = "== 'linux'"}
parse = "*"
importlib-metadata = "*"
colorama= {version = "*", sys_platform = "== 'win32'"}
myst-parser = {extras = ["linkify"], version = "*"}
invoke = "*"
exceptiongroup = "==1.1.0"
pyyaml = "==6.0.1"
build = "*"
twine = "*"
semver = "*"
pypiserver = "2.3.2"
zipp = "==3.21.0"

[packages]
pytz = "*"

[scripts]
tests = "bash ./run-tests.sh"
test = "pytest -vvs"

[pipenv]
allow_prereleases = true
