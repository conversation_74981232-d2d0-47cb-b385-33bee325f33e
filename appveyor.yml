build: false
environment:
  matrix:
    - PYTHON_VERSION: 27
    - PYTHON_VERSION: 33
    - PYTHON_VERSION: 34
    - PYTHON_VERSION: 35
    - PYTHON_VERSION: 36
platform:
  - x64
  - x86

matrix:
  fast_finish: true
  exclude:
    - platform: x86
      PYTHON_VERSION: 33
    - platform: x86
      PYTHON_VERSION: 34
    - platform: x86
      PYTHON_VERSION: 35
    
install:
  # set env variables
  - set TOXENV=py%PYTHON_VERSION%
  - if %PLATFORM% == "X64" (set PYTHON_PATH=C:\Python%PYTHON_VERSION%-x64) ELSE (set PYTHON_PATH=C:\Python%PYTHON_VERSION%)

  # Add PostgreSQL (zic), Python and scripts directory to current path
  - set PATH=%PYTHON_PATH%;c:\Program Files\PostgreSQL\9.3\bin\;%PATH%
  - set PYTHON=%PYTHON_PATH%/python.exe
  - "%PYTHON% -c \"import sys; print(sys.executable, sys.version)\""

  # This frequently fails with network errors, so we'll retry it up to 5 times
  # with a 1 minute rate limit.
  - "%PYTHON% -m pip install six"
  - "ci_tools/retry.bat %PYTHON% updatezinfo.py"
  # This environment variable tells the test suite it's OK to mess with the time zone.
  - set DATEUTIL_MAY_CHANGE_TZ=1
  - C:\Python36\python -m pip install -U tox

test_script:
  - C:\Python36\scripts\tox

after_test:
  - C:\Python36\scripts\tox -e coverage,codecov
