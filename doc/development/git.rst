
New to GitHub or Git?
==========================

Our development workflow is build around Git and GitHub.
Please take your time to become familiar with these.
If you are new to GitHub,
`GitHub has instructions <https://help.github.com/categories/bootcamp/>`_
for getting you started.
If you are new to Git there are a
`tutorial <https://git-scm.com/docs/gittutorial>`_ and an
`excellent book available online <https://git-scm.com/book>`_.


.. Don't use a toctree here to avoid the sections to show up twice in then
   toc of development/index.

**Further Reading**

* :ref:`commit messages`
* :ref:`creating pull-requests`
* :ref:`updating pull-request`
* :ref:`branch model`


.. include:: ../_common_definitions.txt

.. Emacs config:
 Local Variables:
 mode: rst
 ispell-local-dictionary: "american"
 End:
