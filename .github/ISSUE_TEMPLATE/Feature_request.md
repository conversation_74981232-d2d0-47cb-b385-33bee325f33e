---
name: Feature request
about: Suggest an idea for this project
---

Be sure to check the existing issues (both open and closed!), and make sure you are running the latest version of Pipenv.

Check the [diagnose documentation](https://pipenv.pypa.io/en/latest/diagnose/) for common issues as well as the GitHub Issues page.

Make sure to mention your debugging experience if the documented solution failed.

### Is your feature request related to a problem? Please describe.

A clear and concise description of what the problem is. Ex. I'm always frustrated when [...]

### Describe the solution you'd like

A clear and concise description of what you want to happen.

### Describe alternatives you've considered

A clear and concise description of any alternative solutions or features you've considered.

### Additional context

Add any other context or screenshots about the feature request here. It may be a good idea to mention that platform and Python version you are on.

-------------------------------------------------------------------------------

Please run `$ pipenv --support`, and paste the results here. Don't put backticks (`` ` ``) around it! The output already contains Markdown formatting.

If you're on macOS, run the following:

    $ pipenv --support | pbcopy

If you're on Windows, run the following:

    > pipenv --support | clip

If you're on Linux, run the following:

    $ pipenv --support | xclip
