[sdist]
# For release distribution generate .tar.gz archives only. These are
# about 10% smaller then .zip files.
formats=gztar

#[bdist_wheel]
# We MUST NOT create an universal wheel as PyInstaller has different
# dependencies per platforms and version and includes compiled binaries.
#universal = MUST NOT


[zest.releaser]
python-file-with-version = PyInstaller/__init__.py
# This entry point ensures signing of tgz/zip archive before uploading to PYPI.
releaser.before_upload = PyInstaller.utils.release.sign_source_distribution
push-changes = no
tag-format = v{version}
tag-message = PyInstaller {version}
tag-signing = yes


[catchlog]
# Restrict log-level to DEBUG because py.test cannot handle the volume of
# messages that TRACE produces.
log_level = DEBUG

[tool:pytest]
# Do not put timeout to all tests because it does not play nice with running
# tests in parallel. Rather put timeout to single tests: that are known to
#      @pytest.mark.timeout(timeout=0)
# 'thread' timeout method adds more overhead but works in Travis containers.
timeout_method = thread

# Look for tests only in tests directories.
# Later we could change this to just "tests/**/test_*.py"
python_files = "tests/functional/test_*.py" "tests/unit/test_*.py"

# Don't search test-data for test-cases
norecursedirs:
   tests/functional/data
   tests/functional/logs
   tests/functional/modules
   tests/functional/scripts
   tests/functional/specs
   tests/scripts
   tests/unit/Tree_files
   tests/unit/hookutils_files
   tests/unit/test_modulegraph/testdata
   tests/unit/test_modulegraph/testpkg-*


# Display summary info for (s)skipped, (X)xpassed, (x)xfailed, (f)failed and (e)errored tests
# Skip doctest text files
# If you want to run just a subset of test use command
#
#   py.test -k test_name
#
addopts = "-v" "-rsxXfE" "--doctest-glob="

[flake8]
exclude =
   .git,
   doc/_build,
   build,
   dist,
   bootloader
show-source = True
# E265 - block comment should start with '# '
ignore = E265
