<%!
  from rwbench import dateformat
%>
<%inherit file="layout.html" />
<%namespace file="helpers.html" import="input_field, textarea, form" />
<%def name="page_title()">Index Page</%def>
% for article in articles:
  <% if not article.published: continue %>
<div class="article">
  <h2><a href="${article.href|h}">${article.title|h}</a></h2>
  <p class="meta">written by <a href="${article.user.href|h
    }">${article.user.username|h}</a> on ${dateformat(article.pub_date)}</p>
  <div class="text">${article.body}</div>
</div>
% endfor
<%call expr="form()">
  <dl>
    <dt>Name</dt>
    <dd>${input_field('name')}</dd>
    <dt>E-Mail</dt>
    <dd>${input_field('email')}</dd>
    <dt>URL</dt>
    <dd>${input_field('url')}</dd>
    <dt>Comment</dt>
    <dd>${textarea('comment')}</dd>
    <dt>Captcha</dt>
    <dd>${input_field('captcha')}</dd>
  </dl>
  ${input_field(type='submit', value='Submit')}
  ${input_field(name='cancel', type='submit', value='Cancel')}
</%call>
