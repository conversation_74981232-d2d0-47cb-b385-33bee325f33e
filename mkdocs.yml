site_name: Pinax Documentation

pages:
  - index.md
  - Introduction:
    - 'What is Pinax?': what_is_pinax.md
    - Quick Start: quick_start.md
    - History: history.md
    - FAQs: faq.md
  - Starter Projects and Apps:
    - Pinax Starter Projects: pinax_starter_projects.md
    - Pinax Apps: pinax_apps.md
  - How Tos:
    - LDAP Integration: how-tos/ldap.md
    - Deploying to Heroku: how-tos/deploy-to-heroku.md
    - Releasing a Starter Project: how-tos/release-starter-project.md
  - Development:
    - How to Contribute: how_to_contribute.md
    - Ways to Contribute: ways_to_contribute.md
    - Release Process: release_process.md
    - Code of Conduct: code_of_conduct.md
  - In the Wild:
    - In the Wild: in_the_wild.md

theme: readthedocs
